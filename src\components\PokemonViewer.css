.pokemon-viewer-container {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  border: 2px solid #00ff00;
  border-radius: 12px;
  padding: 20px;
  margin: 15px 0;
  color: #00ff00;
  font-family: 'Courier New', monospace;
  box-shadow: 0 0 20px rgba(0, 255, 0, 0.3);
  /* Prevent terminal auto-scroll on click */
  position: relative;
  isolation: isolate;
}

.pokemon-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(0, 255, 0, 0.3);
}

.pokemon-header h3 {
  margin: 0;
  color: #00ff00;
  font-size: 1.2em;
  text-transform: capitalize;
}

.pokemon-id {
  background: rgba(0, 255, 0, 0.2);
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: bold;
  border: 1px solid rgba(0, 255, 0, 0.5);
}

.pokemon-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
}

.pokemon-model-container {
  width: 100%;
  max-width: 600px;
}

.view-toggle {
  display: flex;
  gap: 10px;
  justify-content: center;
  margin-bottom: 20px;
}

.toggle-btn {
  background: rgba(0, 255, 0, 0.1);
  border: 2px solid rgba(0, 255, 0, 0.3);
  color: #00ff00;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
  transition: all 0.3s ease;
  /* Prevent event bubbling */
  position: relative;
  z-index: 1;
}

.toggle-btn:hover:not(:disabled) {
  background: rgba(0, 255, 0, 0.2);
  border-color: rgba(0, 255, 0, 0.6);
  box-shadow: 0 0 10px rgba(0, 255, 0, 0.3);
}

.toggle-btn.active {
  background: rgba(0, 255, 0, 0.3);
  border-color: #00ff00;
  box-shadow: 0 0 15px rgba(0, 255, 0, 0.4);
}

.toggle-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.form-selector {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  margin-bottom: 20px;
  padding: 15px;
  background: rgba(0, 255, 0, 0.05);
  border: 1px solid rgba(0, 255, 0, 0.2);
  border-radius: 8px;
}

.form-label {
  color: #00ff00;
  font-size: 0.9em;
  font-weight: bold;
  margin: 0;
}

.form-dropdown {
  background: rgba(0, 0, 0, 0.7);
  border: 2px solid rgba(0, 255, 0, 0.3);
  color: #00ff00;
  padding: 8px 12px;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 200px;
  /* Prevent event bubbling */
  position: relative;
  z-index: 2;
}

.form-dropdown:hover {
  border-color: rgba(0, 255, 0, 0.6);
  box-shadow: 0 0 8px rgba(0, 255, 0, 0.2);
}

.form-dropdown:focus {
  outline: none;
  border-color: #00ff00;
  box-shadow: 0 0 12px rgba(0, 255, 0, 0.4);
}

.form-dropdown option {
  background: rgba(0, 0, 0, 0.9);
  color: #00ff00;
  padding: 8px;
}

.model-3d-container {
  width: 100%;
  height: 450px;
  background: radial-gradient(circle, rgba(0, 255, 0, 0.05) 0%, transparent 70%);
  border: 2px solid rgba(0, 255, 0, 0.3);
  border-radius: 12px;
  overflow: hidden;
  position: relative;
}

.pokemon-image-container {
  position: relative;
  width: 200px;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: radial-gradient(circle, rgba(0, 255, 0, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  margin: 0 auto 15px;
}

.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #00ff00;
}

.loading-spinner {
  font-size: 2em;
  animation: pulse 1.5s infinite;
}

.loading-text {
  margin-top: 10px;
  font-size: 0.9em;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.pokemon-artwork {
  max-width: 180px;
  max-height: 180px;
  opacity: 0;
  transition: opacity 0.3s ease;
  filter: drop-shadow(0 0 10px rgba(0, 255, 0, 0.3));
}

.pokemon-artwork.loaded {
  opacity: 1;
}

.error-fallback {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #ff6b6b;
}

.error-icon {
  font-size: 3em;
  margin-bottom: 10px;
}

.error-text {
  font-size: 0.9em;
}

.pokemon-types {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  justify-content: center;
}

.type-badge {
  padding: 6px 12px;
  border-radius: 20px;
  color: white;
  font-weight: bold;
  text-transform: uppercase;
  font-size: 0.8em;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.pokemon-stats {
  margin-bottom: 20px;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid rgba(0, 255, 0, 0.2);
}

.stat-label {
  font-weight: bold;
  color: #00ff00;
}

.stat-value {
  color: #ffffff;
  text-transform: capitalize;
}

.pokemon-abilities {
  margin-bottom: 20px;
}

.pokemon-abilities h4 {
  margin: 0 0 10px 0;
  color: #00ff00;
  font-size: 1em;
}

.abilities-list {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.ability-badge {
  background: rgba(0, 255, 0, 0.2);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8em;
  border: 1px solid rgba(0, 255, 0, 0.4);
  text-transform: capitalize;
}

.hidden-tag {
  color: #ffaa00;
  font-style: italic;
  margin-left: 4px;
}

.pokemon-base-stats {
  margin-top: 20px;
}

.pokemon-base-stats h4 {
  margin: 0 0 15px 0;
  color: #00ff00;
  font-size: 1em;
}

.stats-grid {
  display: grid;
  gap: 8px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.stat-name {
  min-width: 100px;
  font-size: 0.8em;
  color: #00ff00;
  text-transform: capitalize;
}

.stat-bar {
  flex: 1;
  height: 20px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  position: relative;
  border: 1px solid rgba(0, 255, 0, 0.3);
  overflow: hidden;
}

.stat-fill {
  height: 100%;
  background: linear-gradient(90deg, #ff6b6b 0%, #ffd93d 50%, #6bcf7f 100%);
  border-radius: 10px;
  transition: width 0.8s ease;
}

.stat-number {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 0.7em;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.pokemon-forms-info {
  margin-top: 20px;
  padding: 15px;
  background: rgba(0, 255, 0, 0.05);
  border: 1px solid rgba(0, 255, 0, 0.2);
  border-radius: 8px;
}

.pokemon-forms-info h4 {
  margin: 0 0 15px 0;
  color: #00ff00;
  font-size: 1em;
}

.forms-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 10px;
  margin-bottom: 15px;
}

.form-card {
  background: rgba(0, 255, 0, 0.1);
  border: 2px solid rgba(0, 255, 0, 0.3);
  border-radius: 8px;
  padding: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  position: relative;
  /* Prevent event bubbling */
  z-index: 1;
}

.form-card:hover {
  background: rgba(0, 255, 0, 0.15);
  border-color: rgba(0, 255, 0, 0.5);
  box-shadow: 0 0 10px rgba(0, 255, 0, 0.2);
}

.form-card.active {
  background: rgba(0, 255, 0, 0.2);
  border-color: #00ff00;
  box-shadow: 0 0 15px rgba(0, 255, 0, 0.3);
}

.form-name {
  color: #00ff00;
  font-size: 0.9em;
  font-weight: bold;
  display: block;
}

.current-indicator {
  color: #ffaa00;
  font-size: 0.7em;
  margin-top: 4px;
  display: block;
  font-style: italic;
}

.forms-description {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(0, 255, 0, 0.2);
  border-radius: 6px;
  padding: 10px;
  margin-top: 10px;
}

.forms-description p {
  margin: 0;
  color: #cccccc;
  font-size: 0.8em;
  line-height: 1.4;
}

.forms-description strong {
  color: #00ff00;
}

/* Responsive design */
@media (max-width: 768px) {
  .pokemon-viewer-container {
    padding: 15px;
  }

  .pokemon-model-container {
    max-width: 100%;
  }

  .model-3d-container {
    height: 350px;
  }

  .toggle-btn {
    padding: 6px 12px;
    font-size: 0.8em;
  }

  .pokemon-image-container {
    width: 150px;
    height: 150px;
  }

  .pokemon-artwork {
    max-width: 130px;
    max-height: 130px;
  }

  .stat-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .form-selector {
    padding: 12px;
  }

  .form-dropdown {
    min-width: 180px;
    font-size: 0.8em;
    padding: 6px 10px;
  }

  .forms-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 8px;
  }

  .form-card {
    padding: 8px;
  }

  .form-name {
    font-size: 0.8em;
  }

  .forms-description p {
    font-size: 0.75em;
  }
}
